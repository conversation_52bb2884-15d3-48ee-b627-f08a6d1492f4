package usecases

import (
	"database/sql"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type WallpaperPlacement string

const (
	NoWallpaper WallpaperPlacement = "None"
	AllWalls    WallpaperPlacement = "AllWalls"
	VanityWall  WallpaperPlacement = "VanityWall"
)

type WallTilePlacement string

const (
	NoWallTile     WallTilePlacement = "None"
	FullWall       WallTilePlacement = "FullWall"
	HalfWall       WallTilePlacement = "HalfWall"
	VanityFullWall WallTilePlacement = "VanityFullWall"
	VanityHalfWall WallTilePlacement = "VanityHalfWall"
)

type TilePattern string

const (
	HorizontalStacked TilePattern = "Horizontal"
	VerticalStacked   TilePattern = "Vertical"
	HalfOffset        TilePattern = "HalfOffset"
	ThirdOffset       TilePattern = "ThirdOffset"
	Herringbone       TilePattern = "Herringbone"
)

type DesignStatus string

const (
	Preview  DesignStatus = "Preview"
	Fave     DesignStatus = "Fave"
	Archived DesignStatus = "Archived"
)

// ColorScheme represents the pallette of a design.
type ColorScheme string

const (
	Neutral ColorScheme = "Neutral"
	Bold    ColorScheme = "Bold"
)

type Style string

const (
	Traditional  Style = "Traditional"
	Transitional Style = "Transitional"
	MidCentury   Style = "Mid-century"
	Modern       Style = "Modern"
)

type FixedProductSelections struct {
	Toilet          *uuid.UUID `json:"toilet,omitempty"`
	Mirror          *uuid.UUID `json:"mirror,omitempty"`
	Lighting        *uuid.UUID `json:"lighting,omitempty"`
	Paint           *uuid.UUID `json:"paint,omitempty"`
	Shelving        *uuid.UUID `json:"shelving,omitempty"`
	FloorTile       *uuid.UUID `json:"floorTile,omitempty"`
	ShowerFloorTile *uuid.UUID `json:"showerFloorTile,omitempty"`
	ShowerWallTile  *uuid.UUID `json:"showerWallTile,omitempty"`
	TubFiller       *uuid.UUID `json:"tubFiller,omitempty"`
	Wallpaper       *uuid.UUID `json:"wallpaper,omitempty"`
	WallTile        *uuid.UUID `json:"wallTile,omitempty"`
}

type DesignOptions struct {
	ColorScheme *ColorScheme
	Style       *Style
	Title       sql.NullString
	Description sql.NullString
	FixedProductSelections
	Faucet                 *uuid.UUID
	FloorTilePattern       *TilePattern
	ShowerFloorTilePattern *TilePattern
	ShowerSystem           *uuid.UUID
	ShowerWallTilePattern  *TilePattern
	ShowerShortWallTile    *uuid.UUID
	ShowerGlass            *uuid.UUID
	Tub                    *uuid.UUID
	TubDoor                *uuid.UUID
	Vanity                 *uuid.UUID
	WallTilePattern        *TilePattern
	NicheTile              *uuid.UUID

	NumSKUs           sql.NullInt32
	TotalPriceInCents sql.NullInt32
	LeadTimeDays      sql.NullInt32
}
type Design struct {
	ID          uuid.UUID
	ProjectID   entities.ProjectId
	Created     time.Time
	LastUpdated time.Time
	Status      DesignStatus

	WallpaperPlacement WallpaperPlacement
	WallTilePlacement  WallTilePlacement
	DesignOptions
	ShowerGlassVisible bool
	TubDoorVisible     bool
	NichesVisible      bool

	Renditions []entities.Rendition
}
type DesignDiff struct {
	ID                 uuid.UUID
	Status             *DesignStatus
	WallpaperPlacement *WallpaperPlacement
	WallTilePlacement  *WallTilePlacement
	DesignOptions
	ShowerGlassVisible sql.NullBool
	TubDoorVisible     sql.NullBool
	NichesVisible      sql.NullBool
}

func MergeDesigns(existing Design, incoming DesignDiff) Design {
	if incoming.Status != nil {
		existing.Status = *incoming.Status
	}
	if incoming.ColorScheme != nil {
		existing.ColorScheme = incoming.ColorScheme
	}
	if incoming.Style != nil {
		existing.Style = incoming.Style
	}
	if incoming.Title.Valid {
		existing.Title = incoming.Title
	}
	if incoming.Description.Valid {
		existing.Description = incoming.Description
	}
	if incoming.FloorTile != nil {
		existing.FloorTile = incoming.FloorTile
	}
	if incoming.Toilet != nil {
		existing.Toilet = incoming.Toilet
	}
	if incoming.Vanity != nil {
		existing.Vanity = incoming.Vanity
	}
	if incoming.Faucet != nil {
		existing.Faucet = incoming.Faucet
	}
	if incoming.Mirror != nil {
		existing.Mirror = incoming.Mirror
	}
	if incoming.FloorTilePattern != nil {
		existing.FloorTilePattern = incoming.FloorTilePattern
	}
	if incoming.Lighting != nil {
		existing.Lighting = incoming.Lighting
	}
	if incoming.NicheTile != nil {
		existing.NicheTile = incoming.NicheTile
	}
	if incoming.Paint != nil {
		existing.Paint = incoming.Paint
	}
	if incoming.Shelving != nil {
		existing.Shelving = incoming.Shelving
	}
	if incoming.ShowerFloorTile != nil {
		existing.ShowerFloorTile = incoming.ShowerFloorTile
	}
	if incoming.ShowerFloorTilePattern != nil {
		existing.ShowerFloorTilePattern = incoming.ShowerFloorTilePattern
	}
	if incoming.ShowerSystem != nil {
		existing.ShowerSystem = incoming.ShowerSystem
	}
	if incoming.ShowerWallTile != nil {
		existing.ShowerWallTile = incoming.ShowerWallTile
	}
	if incoming.ShowerWallTilePattern != nil {
		existing.ShowerWallTilePattern = incoming.ShowerWallTilePattern
	}
	if incoming.ShowerShortWallTile != nil {
		existing.ShowerShortWallTile = incoming.ShowerShortWallTile
	}
	if incoming.ShowerGlass != nil {
		existing.ShowerGlass = incoming.ShowerGlass
	}
	if incoming.Tub != nil {
		existing.Tub = incoming.Tub
	}
	if incoming.TubDoor != nil {
		existing.TubDoor = incoming.TubDoor
	}
	if incoming.TubFiller != nil {
		existing.TubFiller = incoming.TubFiller
	}
	if incoming.WallpaperPlacement != nil {
		existing.WallpaperPlacement = *incoming.WallpaperPlacement
	}
	if incoming.Wallpaper != nil {
		existing.Wallpaper = incoming.Wallpaper
	}
	if incoming.WallTilePlacement != nil {
		existing.WallTilePlacement = *incoming.WallTilePlacement
	}
	if incoming.WallTile != nil {
		existing.WallTile = incoming.WallTile
	}
	if incoming.WallTilePattern != nil {
		existing.WallTilePattern = incoming.WallTilePattern
	}
	if incoming.ShowerGlassVisible.Valid {
		existing.ShowerGlassVisible = incoming.ShowerGlassVisible.Bool
	}
	if incoming.TubDoorVisible.Valid {
		existing.TubDoorVisible = incoming.TubDoorVisible.Bool
	}
	if incoming.NichesVisible.Valid {
		existing.NichesVisible = incoming.NichesVisible.Bool
	}
	if incoming.NumSKUs.Valid {
		existing.NumSKUs = incoming.NumSKUs
	}
	if incoming.TotalPriceInCents.Valid {
		existing.TotalPriceInCents = incoming.TotalPriceInCents
	}
	if incoming.LeadTimeDays.Valid {
		existing.LeadTimeDays = incoming.LeadTimeDays
	}
	existing.LastUpdated = time.Now()
	return existing
}
