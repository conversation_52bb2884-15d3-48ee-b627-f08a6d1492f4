package db

import (
	"context"
	"fmt"
	"log"
	"log/slog"

	"github.com/jackc/pgx/v5/pgxpool"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
)

func NewPostgres(ctx context.Context, pgHost string, pgDatabase string, pwd string, logger *slog.Logger) *gateways.Postgres {
	log.Printf("Connecting to database %s on %s.", pgDatabase, pgHost)
	if pwd != "" {
		pwd = "password=" + pwd
	}
	dbCfg := fmt.Sprintf("host=%s user=postgres %s dbname=%s", pgHost, pwd, pgDatabase)
	dbPool, err := pgxpool.New(ctx, dbCfg)
	if err != nil {
		log.Fatalf("Error connecting to database: %v", err)
	}
	return gateways.NewPostgres(dbPool, logger)
}

func NewRelationalDb(ctx context.Context, dbUrl string, logger *slog.Logger) *gateways.RelationalDb {
	dbCfg := fmt.Sprint(dbUrl)
	dbPool, err := pgxpool.New(ctx, dbCfg)
	if err != nil {
		log.Fatalf("Error connecting to database: %v", err)
	}
	return gateways.NewRelationalDb(dbPool)
}
