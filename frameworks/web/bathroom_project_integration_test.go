package web_test

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
)

func TestBathroomProjectEndpointIntegration(t *testing.T) {
	mux := http.NewServeMux()
	logger := slog.Default()
	handler := web.NewBathroomProjectHandler(logger)

	mux.HandleFunc("GET "+previewsSearchPath, handler.HandleGetRandomBathroomProject)
	server := httptest.NewServer(mux)
	defer server.Close()

	resp, err := http.Get(fmt.Sprintf("%s%s%s%s", server.URL, previewsSearchPath, "?designId=", uuid.New().String()))
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.<PERSON>rf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}

	expectedContentType := "application/json"
	if contentType := resp.Header.Get("Content-Type"); contentType != expectedContentType {
		t.Errorf("Expected content type %s, got %s", expectedContentType, contentType)
	}

	var project map[string]any
	if err := json.NewDecoder(resp.Body).Decode(&project); err != nil {
		t.Fatalf("Failed to decode JSON response: %v", err)
	}

	// Validate the response structure matches the JSON schema
	requiredFields := []string{"name", "cost_usd", "num_items", "area_sq_ft", "updated_at"}
	for _, field := range requiredFields {
		if _, exists := project[field]; !exists {
			t.Errorf("Required field %s is missing from response", field)
		}
	}

	t.Logf("Generated project: %+v", project)
}

func TestBathroomProjectEndpointWrongMethod(t *testing.T) {
	mux := http.NewServeMux()
	logger := slog.Default()
	handler := web.NewBathroomProjectHandler(logger)

	mux.HandleFunc("GET "+previewsSearchPath, handler.HandleGetRandomBathroomProject)
	server := httptest.NewServer(mux)
	defer server.Close()

	resp, err := http.Post(server.URL+previewsSearchPath, "application/json", nil)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusMethodNotAllowed {
		t.Errorf("Expected status code %d for POST request, got %d", http.StatusMethodNotAllowed, resp.StatusCode)
	}
}
