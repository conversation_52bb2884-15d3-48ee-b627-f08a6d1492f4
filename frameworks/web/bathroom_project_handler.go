package web

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"math"
	"math/rand"
	"net/http"
	"time"

	"github.com/google/uuid"
)

const path = "/projects/v0/previews/search"

// BathroomProject represents the structure for bathroom renovation project data
type BathroomProject struct {
	Name      string    `json:"name"`
	UpdatedAt time.Time `json:"updated_at"`
	CostUSD   float64   `json:"cost_usd"`
	NumItems  int       `json:"num_items"`
	AreaSqFt  float64   `json:"area_sq_ft"`
	ImageURL  *string   `json:"image_url,omitempty"`
}

type BathroomProjectHandler struct {
	logger *slog.Logger
}

func NewBathroomProjectHandler(logger *slog.Logger) *BathroomProjectHandler {
	if logger == nil {
		logger = slog.Default()
	}
	return &BathroomProjectHandler{logger: logger}
}

// HandleGetRandomBathroomProject generates and returns a random bathroom renovation project
func (h *BathroomProjectHandler) HandleGetRandomBathroomProject(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.URL.Query().Get("designId")
	if designId == "" {
		h.logger.ErrorContext(ctx, "Bathroom project preview requested with no design ID provided.")
		http.Redirect(w, r, fmt.Sprintf("%s?designId=%s", path, uuid.New().String()), http.StatusFound)
		return
	}
	h.logger.InfoContext(ctx, "Handling GET request for random bathroom project", slog.String("designId", designId))
	if _, err := uuid.Parse(designId); err != nil {
		h.logger.ErrorContext(ctx, "Invalid design ID provided", slog.String("designId", designId))
		http.Error(w, "Invalid design ID; must be a valid UUID.", http.StatusBadRequest)
		return
	}
	h.logger.InfoContext(ctx, "Generating random bathroom project data")
	project := h.generateRandomProject()

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	if err := json.NewEncoder(w).Encode(project); err != nil {
		h.logger.ErrorContext(ctx, "Failed to encode bathroom project response", slog.String("error", err.Error()))
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	h.logger.InfoContext(ctx, "Successfully generated bathroom project",
		slog.String("name", project.Name), slog.Float64("cost", project.CostUSD))
}

func (h *BathroomProjectHandler) generateRandomProject() BathroomProject {
	// Predefined project names for variety
	projectNames := []string{
		"Modern Spa Retreat",
		"Classic Victorian Restoration",
		"Contemporary Minimalist Bath",
		"Rustic Farmhouse Renovation",
		"Luxury Master Suite",
		"Compact Urban Refresh",
		"Traditional Family Bathroom",
		"Industrial Loft Conversion",
		"Coastal Cottage Makeover",
		"Art Deco Revival",
	}

	// Sample image URLs (using placeholder service)
	imageURLs := []string{
		"https://picsum.photos/800/600?random=1",
		"https://picsum.photos/800/600?random=2",
		"https://picsum.photos/800/600?random=3",
		"https://picsum.photos/800/600?random=4",
		"https://picsum.photos/800/600?random=5",
	}

	// Generate random values within realistic ranges
	name := projectNames[rand.Intn(len(projectNames))]
	costUSD := 5000 + rand.Float64()*45000 // $5,000 - $50,000
	numItems := 8 + rand.Intn(25)          // 8-32 items
	areaSqFt := 25 + rand.Float64()*175    // 25-200 sq ft

	// Sometimes include an image URL (80% chance)
	var imageURL *string
	if rand.Float64() < 0.8 {
		url := imageURLs[rand.Intn(len(imageURLs))]
		imageURL = &url
	}

	// Generate a recent timestamp (within last 30 days)
	daysAgo := rand.Intn(30)
	updatedAt := time.Now().AddDate(0, 0, -daysAgo)

	return BathroomProject{
		Name:      name,
		CostUSD:   math.Round(costUSD*100) / 100, // Round to 2 decimal places
		NumItems:  numItems,
		AreaSqFt:  math.Round(areaSqFt*100) / 100, // Round to 2 decimal places
		ImageURL:  imageURL,
		UpdatedAt: updatedAt,
	}
}

func RegisterBathroomProjectHandlers(handler *BathroomProjectHandler) {
	http.HandleFunc("GET "+path, handler.HandleGetRandomBathroomProject)
}
