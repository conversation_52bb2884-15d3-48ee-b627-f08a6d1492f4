package adapters

import (
	"encoding/json"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type Preset struct {
	Id string `json:"id"`

	RoomLayout   entities.RoomLayout `json:"roomLayout"`
	Measurements json.RawMessage     `json:"measurements"`
	Design       Design              `json:"design"`
	Rendition    Rendition           `json:"rendition"`
}

func FromUsecasePreset(preset usecases.Preset) Preset {
	return Preset{
		Id:           preset.Id,
		RoomLayout:   preset.RoomLayout,
		Measurements: preset.Measurements,
		Design:       FromUsecaseDesign(preset.Design),
		Rendition:    FromDomainRendition(preset.Rendition),
	}
}
