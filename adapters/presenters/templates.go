package presenters

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type TemplatesPresenter struct {
	w      http.ResponseWriter
	logger *slog.Logger
}

func NewTemplatesPresenter(logger *slog.Logger, w http.ResponseWriter) *TemplatesPresenter {
	if logger == nil {
		logger = slog.Default()
	}
	return &TemplatesPresenter{logger: logger, w: w}
}

func (tp *TemplatesPresenter) PresentError(err error) {
	http.Error(tp.w, err.<PERSON>rror(), http.StatusNotFound)
}

func (tp *TemplatesPresenter) PresentData(ctx context.Context, data any) {
	bytes, err := json.Marshal(data)
	if err != nil {
		tp.logger.ErrorContext(ctx, "Could not marshal template data into JSON",
			slog.String("error", err.Error()))
		http.Error(tp.w, err.<PERSON>rror(), http.StatusInternalServerError)
		return
	}
	tp.w.Header().Set("Access-Control-Allow-Origin", "*")
	tp.w.Header().Set("Content-Type", "application/json")
	if _, err = tp.w.Write(bytes); err != nil {
		http.Error(tp.w, err.Error(), http.StatusInternalServerError)
	}
}

func (tp *TemplatesPresenter) PresentTemplate(ctx context.Context, template usecases.Template) {
	output := adapters.FromUsecaseTemplate(template)
	wrappedOutput := map[string]any{
		"data": []adapters.Template{output},
	}
	tp.PresentData(ctx, wrappedOutput)
}

func (tp *TemplatesPresenter) PresentTemplates(ctx context.Context, templates []usecases.Template) {
	output := make([]adapters.Template, len(templates))
	for i, t := range templates {
		output[i] = adapters.FromUsecaseTemplate(t)
	}
	wrappedOutput := map[string]any{
		"data": output,
	}
	tp.PresentData(ctx, wrappedOutput)
}

// TemplateCreationOutcomePresenter handles the presentation of template creation/mutation outcomes
type TemplateCreationOutcomePresenter struct {
	w      http.ResponseWriter
	logger *slog.Logger
}

func NewTemplateCreationOutcomePresenter(logger *slog.Logger, w http.ResponseWriter) *TemplateCreationOutcomePresenter {
	if logger == nil {
		logger = slog.Default()
	}
	return &TemplateCreationOutcomePresenter{logger: logger, w: w}
}

func (p *TemplateCreationOutcomePresenter) PresentError(err error) {
	p.logger.Error("Error mutating template", slog.String("error message", err.Error()))
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	switch err {
	case usecases.ErrInvalidPayload:
		http.Error(p.w, err.Error(), http.StatusBadRequest)
		return
	case usecases.ErrNotFound:
		http.Error(p.w, err.Error(), http.StatusNotFound)
		return
	case usecases.ErrConflict:
		http.Error(p.w, "Template with the same ID already exists", http.StatusConflict)
		return
	default:
		http.Error(p.w, err.Error(), http.StatusInternalServerError)
		return
	}
}

func (p *TemplateCreationOutcomePresenter) ConveySuccessWithNewResource(template usecases.Template) {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	var zeroUUID uuid.UUID
	if template.ID == uuid.Nil || template.ID == zeroUUID {
		http.Error(p.w, "Missing/invalid ID in created template", http.StatusInternalServerError)
		return
	}
	p.w.Header().Set("Location", fmt.Sprintf("/templates/%s", template.ID))
	p.w.Header().Set("Content-Type", "application/json")
	p.w.WriteHeader(http.StatusCreated)

	output := adapters.FromUsecaseTemplate(template)
	wrappedOutput := map[string]any{
		"data": output,
	}
	response, err := json.MarshalIndent(wrappedOutput, "", "  ")
	if err != nil {
		http.Error(p.w, err.Error(), http.StatusInternalServerError)
		return
	}
	p.w.Write(response)
}
