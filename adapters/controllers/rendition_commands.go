package controllers

import (
	"context"
	"log/slog"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type RenditionWriteController struct {
	logger  *slog.Logger
	creator *usecases.RenditionSaver
}

func NewRenditionWriteController(logger *slog.Logger, creator *usecases.RenditionSaver) *RenditionWriteController {
	if logger == nil {
		logger = slog.Default()
	}
	return &RenditionWriteController{logger: logger, creator: creator}
}

func (r *RenditionWriteController) SaveRendition(ctx context.Context, designId uuid.UUID, rendition adapters.Rendition, presenter usecases.OutcomePresenter) {
	usecaseRendition, err := rendition.ToDomain()
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to convert rendition to usecase", slog.String("error", err.<PERSON>rror()))
		presenter.PresentError(err)
		return
	}
	r.creator.Save(ctx, designId, usecaseRendition, presenter)
}
